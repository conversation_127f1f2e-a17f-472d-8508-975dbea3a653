'use client';

import { ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { AppRoutes } from '@/constants/core.constants';
import { cn } from '@/lib/utils';

export default function RootLayoutFooter() {
  const pathname = usePathname();

  const allNavItems = [
    {
      icon: Store,
      label: 'Marketplace',
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: ShoppingCart,
      label: 'My Orders',
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: User,
      label: 'My Profile',
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  const navItems = allNavItems;

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c] z-50 h-16 pb-safe">
      <div className={cn('flex items-center justify-between h-full px-4')}>
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={item.route}
              href={item.route}
              className={`flex flex-col items-center gap-1 p-3 h-auto transition-colors ${
                item.active
                  ? 'text-[#6ab2f2]'
                  : 'text-[#708499] hover:text-[#f5f5f5]'
              }`}
            >
              <IconComponent className="w-5 h-5" />
              <span className="text-xs font-medium leading-tight">
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </footer>
  );
}
