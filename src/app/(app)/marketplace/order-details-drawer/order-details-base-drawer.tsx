'use client';

import type { ReactNode } from 'react';
import { Drawer } from 'vaul';

interface OrderDetailsBaseDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: ReactNode;
}

export function OrderDetailsBaseDrawer({
  open,
  onOpenChange,
  children,
}: OrderDetailsBaseDrawerProps) {
  return (
    <Drawer.Root
      {...{
        open,
        onOpenChange,
      }}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none">
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-max-h-[90vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />
            <div className="max-w-md mx-auto space-y-6">{children}</div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
