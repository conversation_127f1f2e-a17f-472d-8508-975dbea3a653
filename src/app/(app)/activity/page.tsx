'use client';

import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { getActivityOrders } from '@/api/orders-api';
import { CollectionSelect } from '@/components/ui/collection-select';
import type { OrderEntity } from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useRootContext } from '@/root-context';

import { ActivityTable } from './activity-table';

export default function ActivityPage() {
  const { collections } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | null>(null);
  const [selectedCollection, setSelectedCollection] = useState('');
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const loadOrders = useCallback(
    async (isLoadMore = false, currentLastDoc?: DocumentSnapshot | null) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setOrders([]);
        setLastDoc(null);
      }

      try {
        const filters = {
          collectionId: selectedCollection || undefined,
          limit: 20,
          lastDoc: isLoadMore ? currentLastDoc : null,
        };

        const result = await getActivityOrders(filters);

        if (isLoadMore) {
          setOrders((prev) => [...prev, ...result.orders]);
        } else {
          setOrders(result.orders);
        }

        setLastDoc(result.lastDoc);
        setHasMore(result.hasMore);
      } catch (error) {
        console.error('Error loading activity orders:', error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [selectedCollection],
  );

  const loadMoreOrders = useCallback(() => {
    if (!loadingMore && hasMore) {
      loadOrders(true, lastDoc);
    }
  }, [loadOrders, loadingMore, hasMore, lastDoc]);

  const containerRef = useInfiniteScroll({
    hasMore,
    loading: loadingMore,
    onLoadMore: loadMoreOrders,
  });

  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  return (
    <div className="space-y-4 pb-[75px]" ref={containerRef}>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">Activity</h1>
      </div>

      <div className="flex flex-col gap-4">
        <div className="w-full max-w-sm">
          <CollectionSelect
            animated={isAnimatedCollection}
            collections={collections}
            value={selectedCollection}
            onValueChange={setSelectedCollection}
            placeholder="All Collections"
          />
        </div>

        <ActivityTable
          orders={orders}
          loading={loading}
          loadingMore={loadingMore}
          collections={collections}
        />
      </div>
    </div>
  );
}
