import { useEffect, useState } from 'react';

import type { SortType } from '@/components/shared/marketplace-filters';

interface UseMarketplaceFiltersReturn {
  minPrice: string;
  maxPrice: string;
  selectedCollection: string;
  sortBy: SortType;
  setMinPrice: (value: string) => void;
  setMaxPrice: (value: string) => void;
  setSelectedCollection: (value: string) => void;
  setSortBy: (value: SortType) => void;
  getFilters: () => {
    minPrice?: number;
    maxPrice?: number;
    collectionId?: string;
    sortBy: SortType;
  };
  getDebouncedFilters: () => {
    minPrice?: number;
    maxPrice?: number;
    collectionId?: string;
    sortBy: SortType;
  };
  resetFilters: () => void;
}

export const useMarketplaceFilters = (): UseMarketplaceFiltersReturn => {
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<SortType>('date_desc');

  // Debounced versions of price filters for API calls
  const [debouncedMinPrice, setDebouncedMinPrice] = useState('');
  const [debouncedMaxPrice, setDebouncedMaxPrice] = useState('');

  // Debounce price changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedMinPrice(minPrice);
    }, 500);

    return () => clearTimeout(timer);
  }, [minPrice]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedMaxPrice(maxPrice);
    }, 500);

    return () => clearTimeout(timer);
  }, [maxPrice]);

  const getFilters = () => ({
    minPrice: minPrice ? parseFloat(minPrice) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
    collectionId: selectedCollection !== 'all' ? selectedCollection : undefined,
    sortBy,
  });

  const getDebouncedFilters = () => ({
    minPrice: debouncedMinPrice ? parseFloat(debouncedMinPrice) : undefined,
    maxPrice: debouncedMaxPrice ? parseFloat(debouncedMaxPrice) : undefined,
    collectionId: selectedCollection !== 'all' ? selectedCollection : undefined,
    sortBy,
  });

  const resetFilters = () => {
    setMinPrice('');
    setMaxPrice('');
    setDebouncedMinPrice('');
    setDebouncedMaxPrice('');
    setSelectedCollection('all');
    setSortBy('date_desc');
  };

  return {
    minPrice,
    maxPrice,
    selectedCollection,
    sortBy,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
    getFilters,
    getDebouncedFilters,
    resetFilters,
  };
};
