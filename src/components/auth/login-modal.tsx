'use client';

import { But<PERSON> } from '@telegram-apps/telegram-ui';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

import { TelegramIcon } from '@/components/icons/telegram-icon';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTelegramAuth } from '@/hooks/use-telegram-auth';
import { useRootContext } from '@/root-context';

interface LoginModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  title?: string;
  description?: string;
}

export function LoginModal({
  open = true,
  onOpenChange,
  title = 'Authentication Required',
  description = 'You must be logged in to perform this action.',
}: LoginModalProps) {
  const { currentUser, refetchUser } = useRootContext();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const { authenticate } = useTelegramAuth({
    onSuccess: async () => {
      try {
        await refetchUser();
        onOpenChange?.(false);
      } catch (error) {
        console.error('Error refetching user after auth:', error);
      }
    },
    onError: (error) => {
      console.error('Authentication failed:', error);
      setIsAuthenticating(false);
    },
  });

  const handleSignIn = async () => {
    setIsAuthenticating(true);
    try {
      await authenticate();
    } catch (error) {
      console.error('Authentication failed:', error);
      setIsAuthenticating(false);
    }
  };

  if (currentUser) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">{title}</DialogTitle>
          <DialogDescription className="text-center">
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center space-y-4 py-4">
          <Button
            onClick={handleSignIn}
            disabled={isAuthenticating}
            className="w-full bg-[#0088cc] hover:bg-[#0088cc]/90 text-white border-0 rounded-2xl h-12"
          >
            {isAuthenticating ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                Signing in...
              </>
            ) : (
              <>
                <TelegramIcon className="w-5 h-5 mr-2" />
                Sign in with Telegram
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
